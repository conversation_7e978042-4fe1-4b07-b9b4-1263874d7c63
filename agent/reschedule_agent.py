from typing import List

from livekit.agents import function_tool
from loguru import logger

from agent.base_agent import BaseAgent
from agent.tools.confirm_appointment_selection import (
    confirm_appointment_selection
)
from agent.tools.confirm_appointment_slot import confirm_appointment_slot
from agent.tools.confirm_doctor_selection import confirm_doctor_selection
from agent.tools.confirm_identity import confirm_identity
from agent.tools.find_availabilities import find_availabilities
from models.clinics import ClinicConfig
from models.patients import Patient
from models.user_data import RunContext_T
from services.appointments import reschedule_appointment
from utils.prompts import (
    END_OF_FLOW_PROMPT,
    build_global_system_prompt,
    build_name_prompt
)


def build_system_prompt(
    clinic_config: ClinicConfig, retrieved_patients: List[Patient]
) -> str:
    return f"""
        {build_global_system_prompt(clinic_config)}
        
        
        # Instructions:
       
        Your goal is to **guide the user through rescheduling an existing medical appointment** from start to finish, following the steps below. 
        - Begin the flow immediately (do not introduce yourself with extra chit-chat) and lead the patient through each step, while adhering to the above guidelines. 
        - The appointment rescheduling is only successful when you have called the `confirm_reschedule` tool with all required details and the user has confirmed everything.
            - If the tool is not called, the rescheduling is not considered successful, try to figure out what is blocking you and try to resolve it
            
        ### 1. Patient identity collection
        {build_name_prompt(retrieved_patients, False)}
        **Important**: No need to ask if the patient already came to the clinic as we are in the rescheduling flow.
        
        ### 2. Appointment selection
        Once the patient is identified, you should see the current pending appointments they have.
        - If the patient has only one appointment, ask them to confirm that it is the one they want to reschedule.
        - If the patient has no appointment, stop the flow and inform them that there is no appointment to reschedule.
        - If the patient has multiple appointments, ask them to confirm the one they want to reschedule.
        - Once the appointment to reschedule is confirmed, call the `confirm_appointment_selection` tool with the appointment ID.
        
        ### 3. Find a new date and time
        Ask the patient for the new date and time they want to reschedule the appointment to.
        - Once the patient gave you a date they want to reschedule to, call the `find_availabilities` tool to find availabilities for the patient's appointment motive.
        - If there are availabilities, present them to the patient and ask them to confirm the one they want to reschedule to.
        - Once the patient confirmed the new date and time, call the `confirm_appointment_slot` tool with the appointment datetime.
        
        ### 4. Reschedule confirmation
        Once the patient has selected the appointment they want to reschedule, and you've found a new appointment slot, confirm the rescheduling with them.
            - If the patient confirms, call the `confirm_reschedule` tool.
            - If the patient declines, ask them how they want to proceed.
        Once the rescheduling is confirmed, inform the patient that their appointment has been rescheduled.
    
        ### 5. End of flow
        {END_OF_FLOW_PROMPT}
    """


class RescheduleAgent(BaseAgent):
    name = "reschedule"

    def __init__(self, clinic_config: ClinicConfig, retrieved_patients: List[Patient]):
        tools = [
            confirm_identity,
            confirm_appointment_selection,
            find_availabilities,
            confirm_appointment_slot,
        ]
        if clinic_config.select_doctor:
            tools.append(confirm_doctor_selection)

        super().__init__(
            clinic_config=clinic_config,
            instructions=build_system_prompt(clinic_config, retrieved_patients),
            tools=tools,
        )

    @function_tool()
    async def confirm_reschedule(
        self,
        context: RunContext_T,
    ) -> str:
        """
        Called when the user confirms the appointment they want to reschedule and the new date and time.
        """
        if context.userdata.appointment is None:
            return "No appointment selected for rescheduling"
        if context.userdata.appointment_slot is None:
            return "Rescheduling slot is not confirmed. Collect it again before rescheduling."

        logger.info(
            f"Confirming rescheduling of appointment {context.userdata.appointment.id} from {context.userdata.appointment.start_date} to {context.userdata.appointment_slot.start_date}"
        )
        context.userdata.appointment = await reschedule_appointment(context.userdata)
        context.userdata.is_call_successful = True
        await context.userdata.audio_player.play("./assets/ding.wav")
        return "Appointment rescheduled."
